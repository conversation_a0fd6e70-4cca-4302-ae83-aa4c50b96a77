//
//  IMYNASearchTabContainerView.m
//  ZZIMYMain
//
//  Created by ljh on 2023/8/7.
//

#import "IMYNASearchTabContainerView.h"
#import <IMYBaseKit/IMYBaseKit.h>
#import <IMYBaseKit/IMYPageViewController.h>

@interface IMYNASearchTabContainerView () <IMYPageViewControllerDataSource, IMYPageViewControllerDelegate>

@property (nonatomic, strong) UIView *topBar;
@property (nonatomic, strong) UILabel *titleLabel;

// Tab 相关
@property (nonatomic, strong) UIView *tabContainer;
@property (nonatomic, strong) UIButton *searchHotButton;
@property (nonatomic, strong) UIButton *hotDiscussionButton;
@property (nonatomic, strong) UIView *indicatorView;

// 子视图
@property (nonatomic, strong) IMYNASearchHotspotsView *hotspotsView;
@property (nonatomic, strong) IMYNASearchHotDiscussionView *hotDiscussionView;

// 实验配置
@property (nonatomic, assign, readwrite) NSInteger searchHotExperimentValue;

// 页面控制器
@property (nonatomic, strong, readwrite) IMYPageViewController *pageViewController;

@end

@implementation IMYNASearchTabContainerView

- (instancetype)initWithFrame:(CGRect)frame {
    self = [super initWithFrame:frame];
    if (self) {
        [self setupUI];
    }
    return self;
}

- (void)setupUI {
    self.imy_width = SCREEN_WIDTH;
    
    // 迭代8.95：获取hotlist实验配置
    // 值说明：0-未命中实验，1-实验组1（默认选中搜索热点），2-实验组2（默认选中热门讨论）
    IMYABTestExperiment *searchHotExp = [[IMYABTestManager sharedInstance] experimentForKey:@"hotlist"];
    self.searchHotExperimentValue = [searchHotExp.vars integerForKey:@"searchhot"];
    
    [self setupTopbar];
    [self setupPageViewController];
    self.name = @"搜索热点";
}

- (void)setName:(NSString *)name {
    if (!name.length) {
        return;
    }
    _name = [name copy];
    if (self.searchHotExperimentValue == 0) {
        // 未命中实验，显示单一标题
        _titleLabel.text = _name;
    }
}

// 内部布局复杂，先不用autolayout了
- (void)setupTopbar {
    _topBar = [[UIView alloc] initWithFrame:CGRectMake(0, 0, self.imy_width, 44)];
    [self addSubview:_topBar];
    
    // 迭代8.95：根据实验值决定创建tab还是单一标题
    if (self.searchHotExperimentValue == 1 || self.searchHotExperimentValue == 2) {
        [self setupTabContainer];
    } else {
        [self setupSingleTitle];
    }
}

// 迭代8.95：创建单一标题（原有逻辑）
- (void)setupSingleTitle {
    _titleLabel = [[UILabel alloc] initWithFrame:CGRectMake(12, 0, 200, 44)];
    _titleLabel.font = [UIFont systemFontOfSize:16 weight:UIFontWeightMedium];
    [_titleLabel imy_setTextColorForKey:kCK_Red_A];
    [_topBar addSubview:_titleLabel];
}

// 迭代8.95：创建tab容器
- (void)setupTabContainer {
    _tabContainer = [[UIView alloc] initWithFrame:CGRectMake(12, 0, self.imy_width - 24, 44)];
    [_topBar addSubview:_tabContainer];
    
    // 创建两个tab按钮
    _searchHotButton = [UIButton buttonWithType:UIButtonTypeCustom];
    _searchHotButton.frame = CGRectMake(0, 0, 80, 44);
    [_searchHotButton setTitle:@"搜索热点" forState:UIControlStateNormal];
    _searchHotButton.titleLabel.font = [UIFont systemFontOfSize:16 weight:UIFontWeightMedium]; // 未选中字号16
    [_searchHotButton.titleLabel sizeToFit];
    [_searchHotButton addTarget:self action:@selector(onSearchHotButtonTapped) forControlEvents:UIControlEventTouchUpInside];
    [_tabContainer addSubview:_searchHotButton];

    _hotDiscussionButton = [UIButton buttonWithType:UIButtonTypeCustom];
    _hotDiscussionButton.frame = CGRectMake(_searchHotButton.imy_right + 24, 0, 80, 44);
    [_hotDiscussionButton setTitle:@"热门讨论" forState:UIControlStateNormal];
    _hotDiscussionButton.titleLabel.font = [UIFont systemFontOfSize:16 weight:UIFontWeightMedium]; // 未选中字号16
    [_hotDiscussionButton.titleLabel sizeToFit];
    [_hotDiscussionButton addTarget:self action:@selector(onHotDiscussionButtonTapped) forControlEvents:UIControlEventTouchUpInside];
    [_tabContainer addSubview:_hotDiscussionButton];

    // 创建指示器 - 宽度20，高度3，距离下方8
    _indicatorView = [[UIView alloc] initWithFrame:CGRectMake(0, 44 - 3, 20, 3)];
    [_indicatorView imy_setBackgroundColorForKey:kCK_Red_A];
    [_indicatorView imy_drawAllCornerRadius:1.5];
    [_tabContainer addSubview:_indicatorView];
    
    // 根据实验值设置默认选中状态
    if (self.searchHotExperimentValue == 2) {
        [self selectHotDiscussionTab];
    } else {
        [self selectSearchHotTab];
    }
}

- (void)setupPageViewController {
    // 创建子视图
    [self setupChildViews];

    // 根据实验值决定是否创建页面控制器
    if (self.searchHotExperimentValue == 1 || self.searchHotExperimentValue == 2) {
        _pageViewController = [[IMYPageViewController alloc] init];
        _pageViewController.dataSource = self;
        _pageViewController.delegate = self;
        _pageViewController.cachePageCount = 2;

        // 添加到视图层级
        [self addSubview:_pageViewController.view];
    } else {
        // 无tab模式，直接添加搜索热点视图
        [self addSubview:_hotspotsView];
        _hotspotsView.frame = CGRectMake(0, _topBar.imy_bottom, self.imy_width, _hotspotsView.imy_height);
    }
}

- (void)setupChildViews {
    // 创建搜索热点视图（恢复到原始状态，移除tab相关逻辑）
    _hotspotsView = [IMYNASearchHotspotsView new];
    // 移除 titleLabel，因为容器会管理标题
    _hotspotsView.name = nil;

    // 创建热门讨论视图
    _hotDiscussionView = [IMYNASearchHotDiscussionView new];
    // 移除 titleLabel，因为容器会管理标题
    _hotDiscussionView.name = nil;

    // 设置回调
    [self setupCallbacks];
}

- (void)setupCallbacks {
    @weakify(self);
    
    // 搜索热点回调
    _hotspotsView.onHeightDidChangedBlock = ^(BOOL anim) {
        @strongify(self);
        [self updateContainerHeight];
    };
    
    _hotspotsView.onKeyDidExposuredBlock = ^(IMYNASearchHotspotsKeyModel *keyModel) {
        @strongify(self);
        if (self.onHotspotsKeyDidExposuredBlock) {
            self.onHotspotsKeyDidExposuredBlock(keyModel);
        }
    };
    
    _hotspotsView.onKeyDidPressedBlock = ^(IMYNASearchHotspotsKeyModel *keyModel) {
        @strongify(self);
        if (self.onHotspotsKeyDidPressedBlock) {
            self.onHotspotsKeyDidPressedBlock(keyModel);
        }
    };
    
    _hotspotsView.onEmptyDidClickedBlock = ^{
        @strongify(self);
        if (self.onEmptyDidClickedBlock) {
            self.onEmptyDidClickedBlock();
        }
    };
    
    // 热门讨论回调
    _hotDiscussionView.onHeightDidChangedBlock = ^(BOOL anim) {
        @strongify(self);
        [self updateContainerHeight];
    };
    
    _hotDiscussionView.onKeyDidExposuredBlock = ^(IMYNASearchHotDiscussionKeyModel *keyModel) {
        @strongify(self);
        if (self.onHotDiscussionKeyDidExposuredBlock) {
            self.onHotDiscussionKeyDidExposuredBlock(keyModel);
        }
    };
    
    _hotDiscussionView.onKeyDidPressedBlock = ^(IMYNASearchHotDiscussionKeyModel *keyModel) {
        @strongify(self);
        if (self.onHotDiscussionKeyDidPressedBlock) {
            self.onHotDiscussionKeyDidPressedBlock(keyModel);
        }
    };
    
    _hotDiscussionView.onEmptyDidClickedBlock = ^{
        @strongify(self);
        if (self.onEmptyDidClickedBlock) {
            self.onEmptyDidClickedBlock();
        }
    };
}

#pragma mark - Tab Actions

- (void)onSearchHotButtonTapped {
    [self selectSearchHotTab];
    [_pageViewController setViewControllerAtIndex:0 animated:YES];
}

- (void)onHotDiscussionButtonTapped {
    [self selectHotDiscussionTab];
    [_pageViewController setViewControllerAtIndex:1 animated:YES];
}

- (void)selectSearchHotTab {
    // 设置颜色
    [_searchHotButton setTitleColor:[UIColor imy_colorForKey:kCK_Red_A] forState:UIControlStateNormal];
    [_hotDiscussionButton setTitleColor:[UIColor imy_colorForKey:kCK_Black_C] forState:UIControlStateNormal];

    // 设置字体大小：选中17，未选中16
    _searchHotButton.titleLabel.font = [UIFont systemFontOfSize:17 weight:UIFontWeightMedium];
    [_searchHotButton.titleLabel sizeToFit];
    _hotDiscussionButton.titleLabel.font = [UIFont systemFontOfSize:16 weight:UIFontWeightMedium];
    [_hotDiscussionButton.titleLabel sizeToFit];

    // 移动指示器
    [UIView animateWithDuration:0.25 animations:^{
        self.indicatorView.imy_centerX = self.searchHotButton.imy_centerX;
    }];
}

- (void)selectHotDiscussionTab {
    // 设置颜色
    [_searchHotButton setTitleColor:[UIColor imy_colorForKey:kCK_Black_C] forState:UIControlStateNormal];
    [_hotDiscussionButton setTitleColor:[UIColor imy_colorForKey:kCK_Red_A] forState:UIControlStateNormal];

    // 设置字体大小：选中17，未选中16
    _searchHotButton.titleLabel.font = [UIFont systemFontOfSize:16 weight:UIFontWeightMedium];
    [_searchHotButton.titleLabel sizeToFit];
    _hotDiscussionButton.titleLabel.font = [UIFont systemFontOfSize:17 weight:UIFontWeightMedium];
    [_hotDiscussionButton.titleLabel sizeToFit];
    // 移动指示器
    [UIView animateWithDuration:0.25 animations:^{
        self.indicatorView.imy_centerX = self.hotDiscussionButton.imy_centerX;
    }];
}

#pragma mark - Public Methods

- (void)setupWithHotspotsKeyModels:(NSArray<IMYNASearchHotspotsKeyModel *> *)keyModels
                          animated:(BOOL)animated {
    [_hotspotsView setupWithHotspotsKeyModels:keyModels animated:animated];
}

- (void)setupWithHotDiscussionKeyModels:(NSArray<IMYNASearchHotDiscussionKeyModel *> *)keyModels
                               animated:(BOOL)animated {
    [_hotDiscussionView setupWithHotDiscussionKeyModels:keyModels animated:animated];
}

- (NSArray<IMYNASearchHotspotsKeyModel *> *)hotspotsKeys {
    return _hotspotsView.hotspotsKeys;
}

- (NSArray<IMYNASearchHotDiscussionKeyModel *> *)hotDiscussionKeys {
    return _hotDiscussionView.hotDiscussionKeys;
}

// 迭代8.95：动态控制tab显示状态
- (void)updateTabVisibilityWithHotDiscussionAvailable:(BOOL)hasHotDiscussion animated:(BOOL)animated {
    // 只有在实验组中才需要动态控制tab显示
    if (self.searchHotExperimentValue != 1 && self.searchHotExperimentValue != 2) {
        // 非实验组，始终显示单一热门搜索，无需处理
        return;
    }

    // 判断当前是否已经显示tab
    BOOL currentlyShowingTabs = (_pageViewController != nil && _pageViewController.view.superview != nil);

    if (hasHotDiscussion && !currentlyShowingTabs) {
        // 需要显示tab，但当前没有显示 - 切换到tab模式
        [self switchToTabModeAnimated:animated];
    } else if (!hasHotDiscussion && currentlyShowingTabs) {
        // 不需要显示tab，但当前正在显示 - 切换到单一模式
        [self switchToSingleModeAnimated:animated];
    }
}

#pragma mark - Private Methods

// 迭代8.95：切换到tab模式
- (void)switchToTabModeAnimated:(BOOL)animated {
    if (_pageViewController && _pageViewController.view.superview) {
        // 已经是tab模式，无需切换
        return;
    }

    // 移除单一模式的视图
    [_hotspotsView removeFromSuperview];

    // 重新创建tab相关UI
    [self recreateTabUI];

    // 添加页面控制器
    if (!_pageViewController) {
        _pageViewController = [[IMYPageViewController alloc] init];
        _pageViewController.dataSource = self;
        _pageViewController.delegate = self;
        _pageViewController.cachePageCount = 2;
    }

    [self addSubview:_pageViewController.view];

    // 更新布局
    [self updateContainerHeight];

    if (animated) {
        _pageViewController.view.alpha = 0;
        [UIView animateWithDuration:0.3 animations:^{
            _pageViewController.view.alpha = 1;
        }];
    }
}

// 迭代8.95：切换到单一模式
- (void)switchToSingleModeAnimated:(BOOL)animated {
    if (!_pageViewController || !_pageViewController.view.superview) {
        // 已经是单一模式，无需切换
        return;
    }

    // 移除页面控制器
    [_pageViewController.view removeFromSuperview];

    // 重新创建单一标题UI
    [self recreateSingleTitleUI];

    // 直接添加搜索热点视图
    [self addSubview:_hotspotsView];
    _hotspotsView.frame = CGRectMake(0, _topBar.imy_bottom, self.imy_width, _hotspotsView.imy_height);

    // 更新布局
    [self updateContainerHeight];

    if (animated) {
        _hotspotsView.alpha = 0;
        [UIView animateWithDuration:0.3 animations:^{
            _hotspotsView.alpha = 1;
        }];
    }
}

// 迭代8.95：重新创建tab UI
- (void)recreateTabUI {
    // 清空topBar
    for (UIView *subview in _topBar.subviews) {
        [subview removeFromSuperview];
    }

    // 重新创建tab容器
    [self setupTabContainer];
}

// 迭代8.95：重新创建单一标题UI
- (void)recreateSingleTitleUI {
    // 清空topBar
    for (UIView *subview in _topBar.subviews) {
        [subview removeFromSuperview];
    }

    // 重新创建单一标题
    [self setupSingleTitle];
    _titleLabel.text = _name;
}

- (void)updateContainerHeight {
    CGFloat maxHeight = MAX(_hotspotsView.imy_height, _hotDiscussionView.imy_height);
    CGFloat newHeight;

    if (self.searchHotExperimentValue == 1 || self.searchHotExperimentValue == 2) {
        // 有tab时
        newHeight = _topBar.imy_bottom + maxHeight;
        _pageViewController.view.frame = CGRectMake(0, _topBar.imy_bottom + 8, self.imy_width, maxHeight);
    } else {
        // 无tab时，直接显示搜索热点
        newHeight = _topBar.imy_bottom + _hotspotsView.imy_height;
    }

    if (self.imy_height != newHeight) {
        self.imy_height = newHeight;

        if (self.onHeightDidChangedBlock) {
            self.onHeightDidChangedBlock(NO);
        }
    }
}

- (void)layoutSubviews {
    [super layoutSubviews];
    [self updateContainerHeight];
}

#pragma mark - IMYPageViewControllerDataSource

- (NSUInteger)numberOfControllersInPageViewController:(IMYPageViewController *)pageViewController {
    // 根据实验值决定页面数量
    if (self.searchHotExperimentValue == 1 || self.searchHotExperimentValue == 2) {
        return 2; // 有tab时显示两个页面
    } else {
        return 1; // 无tab时只显示搜索热点
    }
}

- (UIViewController *)pageViewController:(IMYPageViewController *)pageViewController controllerAtIndex:(NSUInteger)index {
    UIViewController *containerVC = [[UIViewController alloc] init];
    containerVC.view.backgroundColor = [UIColor clearColor];

    if (index == 0) {
        // 搜索热点页面
        // 确保视图没有被添加到其他父视图
        [_hotspotsView removeFromSuperview];
        [containerVC.view addSubview:_hotspotsView];
        _hotspotsView.frame = CGRectMake(0, 0, self.imy_width, _hotspotsView.imy_height);
    } else if (index == 1) {
        // 热门讨论页面
        // 确保视图没有被添加到其他父视图
        [_hotDiscussionView removeFromSuperview];
        [containerVC.view addSubview:_hotDiscussionView];
        _hotDiscussionView.frame = CGRectMake(0, 0, self.imy_width, _hotDiscussionView.imy_height);
    }

    return containerVC;
}

#pragma mark - IMYPageViewControllerDelegate

- (void)pageViewController:(IMYPageViewController *)pageViewController didTransitionFromIndex:(NSUInteger)fromIndex toIndex:(NSUInteger)toIndex {
    // 同步tab状态
    if (toIndex == 0) {
        [self selectSearchHotTab];
    } else if (toIndex == 1) {
        [self selectHotDiscussionTab];
    }
}

- (void)pageViewController:(IMYPageViewController *)pageViewController willTransitionFromIndex:(NSUInteger)fromIndex toIndex:(NSUInteger)toIndex {
    // 收起键盘
    [[UIApplication sharedApplication].keyWindow endEditing:YES];
}

@end
