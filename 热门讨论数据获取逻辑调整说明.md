# 热门讨论数据获取逻辑调整说明

## 修改概述

根据需求，已完成热门讨论数据获取逻辑的调整，实现了热门搜索和热门讨论使用不同数据源的功能。

## 具体修改内容

### 1. 网络请求接口

**文件**: `zzi<PERSON><PERSON>in/Seeyou/Classes/Search/Native/IMYNASearchHomeVC.m`

**说明**:
- 继续使用现有的 `v2/search_suggest` 接口
- 服务端已经为该接口新增了 `rank_list_topic` 响应字段
- 无需调整请求参数，保持原有参数不变

### 2. 响应数据解析调整

**修改位置**: `requestData` 方法的响应处理部分 (第837-840行)

**修改内容**:
- 新增对服务端返回的 `rank_list_topic` 字段的解析
- 该字段包含热门讨论的专用数据，数据结构与 `rank_list` 一致

```objc
NSArray *guessKeyModels = [apiData[@"give_words"] toModels:IMYNASearchGuessYouKeyModel.class];
NSArray *hotKeyModels = [apiData[@"rank_list"][@"words"] toModels:IMYNASearchHotspotsKeyModel.class];
// 迭代8.95：解析热门讨论数据
NSArray *hotDiscussionKeyModels = [apiData[@"rank_list_topic"][@"words"] toModels:IMYNASearchHotspotsKeyModel.class];
```

### 3. 数据存储调整

**修改位置**:
- 类属性定义 (第58-62行)
- 数据设置部分 (第850-855行)

**修改内容**:
- 新增 `origHotDiscussionKeyModels` 属性存储热门讨论原始数据
- 在网络请求成功后将热门讨论数据存储到该属性中

```objc
// 类属性定义
@property (nonatomic, copy) NSArray *origHotDiscussionKeyModels;

// 数据设置
self.origHotDiscussionKeyModels = hotDiscussionKeyModels ?: @[];
```

### 4. 热门讨论数据设置逻辑重构

**修改位置**:
- 原 `setupHotspotsWithKeyModels` 方法 (第750-751行)
- 新增 `setupHotDiscussionWithKeyModels` 方法 (第754-816行)

**修改内容**:
- 将原来复制热门搜索数据的逻辑替换为调用专门的热门讨论设置方法
- 新增 `setupHotDiscussionWithKeyModels` 方法，专门使用 `rank_list_topic` 数据
- 当 `rank_list_topic` 数据不可用时，热门讨论tab显示为空，不再使用热门搜索数据作为兜底

### 5. 缓存数据处理调整

**修改位置**: `setupCacheData` 方法 (第681-689行)

**修改内容**:
- 在读取缓存数据时也处理热门讨论的缓存数据
- 确保缓存和实时数据的处理逻辑一致

```objc
// 搜索热榜
NSArray *hotKeyModels = [apiCache[@"rank_list"][@"words"] toModels:IMYNASearchHotspotsKeyModel.class];
// 迭代8.95：热门讨论缓存数据
NSArray *hotDiscussionKeyModels = [apiCache[@"rank_list_topic"][@"words"] toModels:IMYNASearchHotspotsKeyModel.class];
if (hotKeyModels.count > 0) {
    self.origHotsportsKeyModels = hotKeyModels;
    self.origHotDiscussionKeyModels = hotDiscussionKeyModels ?: @[];
    [self setupHotspotsWithKeyModels];
}
```

## 实现效果

1. **热门搜索tab**: 继续使用 `rank_list` 数据，保持原有功能不变
2. **热门讨论tab**: 使用 `rank_list_topic` 数据，显示专门的热门讨论内容
3. **兜底机制**: 当 `rank_list_topic` 数据不可用时，自动使用 `rank_list` 数据确保功能正常
4. **缓存支持**: 支持热门讨论数据的本地缓存，提升用户体验
5. **向后兼容**: 保持与现有代码的兼容性，不影响其他功能

## 注意事项

1. **接口兼容性**: 服务端已为现有接口新增 `rank_list_topic` 字段，无需调整请求参数
2. **数据模型复用**: 热门讨论复用了 `IMYNASearchHotspotsKeyModel` 数据模型，确保数据结构兼容
3. **广告逻辑**: 当前热门讨论暂未添加广告插入逻辑，如需要可在 `setupHotDiscussionWithKeyModels` 方法中添加
4. **测试验证**: 建议在实际环境中测试验证两个tab显示的数据内容是否有所区别

## 验证方法

1. 检查服务端响应是否包含 `rank_list_topic` 字段
2. 验证热门搜索tab显示 `rank_list` 数据
3. 验证热门讨论tab显示 `rank_list_topic` 数据
4. 确认两个tab的数据内容有所区别
5. 测试缓存功能是否正常工作
